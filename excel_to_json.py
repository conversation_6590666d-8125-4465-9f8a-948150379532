#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel文件转JSON文件脚本
将Excel文件中的问答对数据转换为独立的JSON文件
"""

import pandas as pd
import json
import os
from pathlib import Path

def excel_to_json_files(excel_path, output_folder="问答对", start_index=1):
    """
    将Excel文件转换为多个JSON文件
    
    Args:
        excel_path: Excel文件路径
        output_folder: 输出文件夹路径
        start_index: 起始编号（默认从1开始）
    
    Returns:
        转换成功的文件数量
    """
    try:
        # 读取Excel文件
        df = pd.read_excel(excel_path)
        print(f"成功读取Excel文件: {excel_path}")
        print(f"数据行数: {len(df)}")
        print(f"列名: {list(df.columns)}")
        
        # 创建输出文件夹
        os.makedirs(output_folder, exist_ok=True)
        
        success_count = 0
        
        # 遍历每一行数据
        for index, row in df.iterrows():
            try:
                # 构建JSON数据结构
                json_data = {}
                
                # 映射Excel列到JSON字段
                if '类别' in row and pd.notna(row['类别']):
                    json_data['类别'] = str(row['类别']).strip()
                else:
                    json_data['类别'] = ""
                
                if '问题' in row and pd.notna(row['问题']):
                    json_data['问题'] = str(row['问题']).strip()
                else:
                    json_data['问题'] = ""
                
                if '回答' in row and pd.notna(row['回答']):
                    json_data['回答'] = str(row['回答']).strip()
                else:
                    json_data['回答'] = ""
                
                if '分类' in row and pd.notna(row['分类']):
                    json_data['分类'] = str(row['分类']).strip()
                else:
                    json_data['分类'] = None
                
                if '优化后的回答' in row and pd.notna(row['优化后的回答']):
                    json_data['优化后的回答'] = str(row['优化后的回答']).strip()
                else:
                    json_data['优化后的回答'] = None
                
                if '是否可用' in row and pd.notna(row['是否可用']):
                    json_data['是否可用'] = str(row['是否可用']).strip()
                else:
                    json_data['是否可用'] = ""
                
                if '原因' in row and pd.notna(row['原因']):
                    json_data['原因'] = str(row['原因']).strip()
                else:
                    json_data['原因'] = None
                
                # 生成文件名
                file_number = start_index + index
                filename = f"{file_number:03d}.json"
                filepath = os.path.join(output_folder, filename)
                
                # 保存JSON文件
                with open(filepath, 'w', encoding='utf-8') as f:
                    json.dump(json_data, f, ensure_ascii=False, indent=2)
                
                success_count += 1
                print(f"已转换: {filename}")
                
            except Exception as e:
                print(f"转换第 {index + 1} 行数据时出错: {e}")
                continue
        
        print(f"\n转换完成!")
        print(f"成功转换: {success_count} 个文件")
        print(f"输出目录: {output_folder}")
        
        return success_count
        
    except Exception as e:
        print(f"读取Excel文件时出错: {e}")
        return 0

def main():
    """主函数"""
    print("Excel转JSON文件转换器")
    print("=" * 50)
    
    # 默认配置
    excel_file = "审核问答对（100-201）.xlsx"
    output_dir = "问答对"
    
    # 检查Excel文件是否存在
    if not os.path.exists(excel_file):
        print(f"错误: Excel文件不存在 - {excel_file}")
        print("请确保文件存在于当前目录中")
        return
    
    # 执行转换
    result = excel_to_json_files(excel_file, output_dir)
    
    if result > 0:
        print(f"\n✓ 转换成功! 共生成 {result} 个JSON文件")
    else:
        print("\n✗ 转换失败!")

if __name__ == "__main__":
    main()
